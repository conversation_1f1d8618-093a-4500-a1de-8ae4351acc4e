/* eslint-disable no-undef */
import { defineConfig } from "vite";
import react from "@vitejs/plugin-react-swc";
import path from "path";
// https://vitejs.dev/config/
export default defineConfig({
  plugins: [react()],
  resolve: {
    alias: {
      "@": path.resolve(__dirname, "./src"),
    },
  },
  server: {
    open: true, // Automatically open the browser
    proxy: {
      "/api/v1/communication/public": {
        target: "http://localhost:4004",
        changeOrigin: true,
        secure: false,
        configure: (proxy, _options) => {
          proxy.on("error", (err, _req, _res) => {
            console.log("proxy error", err);
          });
          proxy.on("proxyReq", (_proxyReq, req, _res) => {
            console.log("Sending Request to the Target:", req.method, req.url);
          });
          proxy.on("proxyRes", (proxyRes, req, _res) => {
            console.log(
              "Received Response from the Target:",
              proxyRes.statusCode,
              req.url
            );
          });
        },
      },
    },
  },
  build: {
    outDir: "build",
    // Generate source maps for better debugging
    sourcemap: true,
    // Use default minification to avoid issues
    minify: true,
    // Increase chunk size warning limit to 1000kb
    chunkSizeWarningLimit: 1000,
    // Copy files from public directory to build output
    rollupOptions: {
      output: {
        manualChunks: (id) => {
          // Vendor chunk for core React libraries
          if (id.includes("node_modules")) {
            if (
              id.includes("react") ||
              id.includes("react-dom") ||
              id.includes("react-router")
            ) {
              return "vendor-react";
            }
            // UI libraries chunk
            if (
              id.includes("bootstrap") ||
              id.includes("swiper") ||
              id.includes("photoswipe")
            ) {
              return "vendor-ui";
            }
            // Animation libraries chunk
            if (
              id.includes("wow") ||
              id.includes("rellax") ||
              id.includes("splitting")
            ) {
              return "vendor-animations";
            }
            // Utility libraries chunk
            if (
              id.includes("axios") ||
              id.includes("zustand") ||
              id.includes("helmet")
            ) {
              return "vendor-utils";
            }
            // All other vendor libraries
            return "vendor-misc";
          }

          // Split pages into separate chunks
          if (id.includes("/pages/")) {
            if (id.includes("/products/")) {
              return "pages-products";
            }
            if (
              id.includes("/about/") ||
              id.includes("/contact/") ||
              id.includes("/services/")
            ) {
              return "pages-static";
            }
            return "pages-other";
          }

          // Split components by type
          if (id.includes("/components/")) {
            if (id.includes("/headers/") || id.includes("/footers/")) {
              return "components-layout";
            }
            if (id.includes("/home/")) {
              return "components-home";
            }
            return "components-common";
          }
        },
      },
    },
  },
});
