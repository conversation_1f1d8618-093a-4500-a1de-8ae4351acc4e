hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/runtime@7.26.9':
    '@babel/runtime': private
  '@esbuild/aix-ppc64@0.21.5':
    '@esbuild/aix-ppc64': private
  '@esbuild/android-arm64@0.21.5':
    '@esbuild/android-arm64': private
  '@esbuild/android-arm@0.21.5':
    '@esbuild/android-arm': private
  '@esbuild/android-x64@0.21.5':
    '@esbuild/android-x64': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@esbuild/darwin-x64@0.21.5':
    '@esbuild/darwin-x64': private
  '@esbuild/freebsd-arm64@0.21.5':
    '@esbuild/freebsd-arm64': private
  '@esbuild/freebsd-x64@0.21.5':
    '@esbuild/freebsd-x64': private
  '@esbuild/linux-arm64@0.21.5':
    '@esbuild/linux-arm64': private
  '@esbuild/linux-arm@0.21.5':
    '@esbuild/linux-arm': private
  '@esbuild/linux-ia32@0.21.5':
    '@esbuild/linux-ia32': private
  '@esbuild/linux-loong64@0.21.5':
    '@esbuild/linux-loong64': private
  '@esbuild/linux-mips64el@0.21.5':
    '@esbuild/linux-mips64el': private
  '@esbuild/linux-ppc64@0.21.5':
    '@esbuild/linux-ppc64': private
  '@esbuild/linux-riscv64@0.21.5':
    '@esbuild/linux-riscv64': private
  '@esbuild/linux-s390x@0.21.5':
    '@esbuild/linux-s390x': private
  '@esbuild/linux-x64@0.21.5':
    '@esbuild/linux-x64': private
  '@esbuild/netbsd-x64@0.21.5':
    '@esbuild/netbsd-x64': private
  '@esbuild/openbsd-x64@0.21.5':
    '@esbuild/openbsd-x64': private
  '@esbuild/sunos-x64@0.21.5':
    '@esbuild/sunos-x64': private
  '@esbuild/win32-arm64@0.21.5':
    '@esbuild/win32-arm64': private
  '@esbuild/win32-ia32@0.21.5':
    '@esbuild/win32-ia32': private
  '@esbuild/win32-x64@0.21.5':
    '@esbuild/win32-x64': private
  '@eslint-community/eslint-utils@4.4.1(eslint@9.20.1)':
    '@eslint-community/eslint-utils': public
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': public
  '@eslint/config-array@0.19.2':
    '@eslint/config-array': public
  '@eslint/core@0.11.0':
    '@eslint/core': public
  '@eslint/eslintrc@3.2.0':
    '@eslint/eslintrc': public
  '@eslint/object-schema@2.1.6':
    '@eslint/object-schema': public
  '@eslint/plugin-kit@0.2.5':
    '@eslint/plugin-kit': public
  '@humanfs/core@0.19.1':
    '@humanfs/core': private
  '@humanfs/node@0.16.6':
    '@humanfs/node': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/retry@0.4.1':
    '@humanwhocodes/retry': private
  '@img/sharp-darwin-arm64@0.34.0':
    '@img/sharp-darwin-arm64': private
  '@img/sharp-darwin-x64@0.34.0':
    '@img/sharp-darwin-x64': private
  '@img/sharp-libvips-darwin-arm64@1.1.0':
    '@img/sharp-libvips-darwin-arm64': private
  '@img/sharp-libvips-darwin-x64@1.1.0':
    '@img/sharp-libvips-darwin-x64': private
  '@img/sharp-libvips-linux-arm64@1.1.0':
    '@img/sharp-libvips-linux-arm64': private
  '@img/sharp-libvips-linux-arm@1.1.0':
    '@img/sharp-libvips-linux-arm': private
  '@img/sharp-libvips-linux-ppc64@1.1.0':
    '@img/sharp-libvips-linux-ppc64': private
  '@img/sharp-libvips-linux-s390x@1.1.0':
    '@img/sharp-libvips-linux-s390x': private
  '@img/sharp-libvips-linux-x64@1.1.0':
    '@img/sharp-libvips-linux-x64': private
  '@img/sharp-libvips-linuxmusl-arm64@1.1.0':
    '@img/sharp-libvips-linuxmusl-arm64': private
  '@img/sharp-libvips-linuxmusl-x64@1.1.0':
    '@img/sharp-libvips-linuxmusl-x64': private
  '@img/sharp-linux-arm64@0.34.0':
    '@img/sharp-linux-arm64': private
  '@img/sharp-linux-arm@0.34.0':
    '@img/sharp-linux-arm': private
  '@img/sharp-linux-s390x@0.34.0':
    '@img/sharp-linux-s390x': private
  '@img/sharp-linux-x64@0.34.0':
    '@img/sharp-linux-x64': private
  '@img/sharp-linuxmusl-arm64@0.34.0':
    '@img/sharp-linuxmusl-arm64': private
  '@img/sharp-linuxmusl-x64@0.34.0':
    '@img/sharp-linuxmusl-x64': private
  '@img/sharp-wasm32@0.34.0':
    '@img/sharp-wasm32': private
  '@img/sharp-win32-ia32@0.34.0':
    '@img/sharp-win32-ia32': private
  '@img/sharp-win32-x64@0.34.0':
    '@img/sharp-win32-x64': private
  '@jridgewell/gen-mapping@0.3.8':
    '@jridgewell/gen-mapping': private
  '@jridgewell/resolve-uri@3.1.2':
    '@jridgewell/resolve-uri': private
  '@jridgewell/set-array@1.2.1':
    '@jridgewell/set-array': private
  '@jridgewell/source-map@0.3.6':
    '@jridgewell/source-map': private
  '@jridgewell/sourcemap-codec@1.5.0':
    '@jridgewell/sourcemap-codec': private
  '@jridgewell/trace-mapping@0.3.25':
    '@jridgewell/trace-mapping': private
  '@remix-run/router@1.22.0':
    '@remix-run/router': private
  '@rollup/rollup-android-arm-eabi@4.34.7':
    '@rollup/rollup-android-arm-eabi': private
  '@rollup/rollup-android-arm64@4.34.7':
    '@rollup/rollup-android-arm64': private
  '@rollup/rollup-darwin-arm64@4.34.7':
    '@rollup/rollup-darwin-arm64': private
  '@rollup/rollup-darwin-x64@4.34.7':
    '@rollup/rollup-darwin-x64': private
  '@rollup/rollup-freebsd-arm64@4.34.7':
    '@rollup/rollup-freebsd-arm64': private
  '@rollup/rollup-freebsd-x64@4.34.7':
    '@rollup/rollup-freebsd-x64': private
  '@rollup/rollup-linux-arm-gnueabihf@4.34.7':
    '@rollup/rollup-linux-arm-gnueabihf': private
  '@rollup/rollup-linux-arm-musleabihf@4.34.7':
    '@rollup/rollup-linux-arm-musleabihf': private
  '@rollup/rollup-linux-arm64-gnu@4.34.7':
    '@rollup/rollup-linux-arm64-gnu': private
  '@rollup/rollup-linux-arm64-musl@4.34.7':
    '@rollup/rollup-linux-arm64-musl': private
  '@rollup/rollup-linux-loongarch64-gnu@4.34.7':
    '@rollup/rollup-linux-loongarch64-gnu': private
  '@rollup/rollup-linux-powerpc64le-gnu@4.34.7':
    '@rollup/rollup-linux-powerpc64le-gnu': private
  '@rollup/rollup-linux-riscv64-gnu@4.34.7':
    '@rollup/rollup-linux-riscv64-gnu': private
  '@rollup/rollup-linux-s390x-gnu@4.34.7':
    '@rollup/rollup-linux-s390x-gnu': private
  '@rollup/rollup-linux-x64-gnu@4.34.7':
    '@rollup/rollup-linux-x64-gnu': private
  '@rollup/rollup-linux-x64-musl@4.34.7':
    '@rollup/rollup-linux-x64-musl': private
  '@rollup/rollup-win32-arm64-msvc@4.34.7':
    '@rollup/rollup-win32-arm64-msvc': private
  '@rollup/rollup-win32-ia32-msvc@4.34.7':
    '@rollup/rollup-win32-ia32-msvc': private
  '@rollup/rollup-win32-x64-msvc@4.34.7':
    '@rollup/rollup-win32-x64-msvc': private
  '@swc/core-darwin-arm64@1.10.16':
    '@swc/core-darwin-arm64': private
  '@swc/core-darwin-x64@1.10.16':
    '@swc/core-darwin-x64': private
  '@swc/core-linux-arm-gnueabihf@1.10.16':
    '@swc/core-linux-arm-gnueabihf': private
  '@swc/core-linux-arm64-gnu@1.10.16':
    '@swc/core-linux-arm64-gnu': private
  '@swc/core-linux-arm64-musl@1.10.16':
    '@swc/core-linux-arm64-musl': private
  '@swc/core-linux-x64-gnu@1.10.16':
    '@swc/core-linux-x64-gnu': private
  '@swc/core-linux-x64-musl@1.10.16':
    '@swc/core-linux-x64-musl': private
  '@swc/core-win32-arm64-msvc@1.10.16':
    '@swc/core-win32-arm64-msvc': private
  '@swc/core-win32-ia32-msvc@1.10.16':
    '@swc/core-win32-ia32-msvc': private
  '@swc/core-win32-x64-msvc@1.10.16':
    '@swc/core-win32-x64-msvc': private
  '@swc/core@1.10.16':
    '@swc/core': private
  '@swc/counter@0.1.3':
    '@swc/counter': private
  '@swc/types@0.1.17':
    '@swc/types': private
  '@types/estree@1.0.6':
    '@types/estree': private
  '@types/json-schema@7.0.15':
    '@types/json-schema': private
  '@types/node@22.14.0':
    '@types/node': private
  '@types/prop-types@15.7.14':
    '@types/prop-types': private
  acorn-jsx@5.3.2(acorn@8.14.0):
    acorn-jsx: private
  acorn@8.14.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  animate.css@4.1.1:
    animate.css: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  array-buffer-byte-length@1.0.2:
    array-buffer-byte-length: private
  array-includes@3.1.8:
    array-includes: private
  array.prototype.findlast@1.2.5:
    array.prototype.findlast: private
  array.prototype.flat@1.3.3:
    array.prototype.flat: private
  array.prototype.flatmap@1.3.3:
    array.prototype.flatmap: private
  array.prototype.tosorted@1.1.4:
    array.prototype.tosorted: private
  arraybuffer.prototype.slice@1.0.4:
    arraybuffer.prototype.slice: private
  async-function@1.0.0:
    async-function: private
  asynckit@0.4.0:
    asynckit: private
  available-typed-arrays@1.0.7:
    available-typed-arrays: private
  balanced-match@1.0.2:
    balanced-match: private
  brace-expansion@1.1.11:
    brace-expansion: private
  buffer-from@1.1.2:
    buffer-from: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  call-bind@1.0.8:
    call-bind: private
  call-bound@1.0.3:
    call-bound: private
  callsites@3.1.0:
    callsites: private
  chalk@4.1.2:
    chalk: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  color-string@1.9.1:
    color-string: private
  color@4.2.3:
    color: private
  combined-stream@1.0.8:
    combined-stream: private
  commander@2.20.3:
    commander: private
  concat-map@0.0.1:
    concat-map: private
  core-js@3.40.0:
    core-js: private
  cross-spawn@7.0.6:
    cross-spawn: private
  csstype@3.1.3:
    csstype: private
  data-view-buffer@1.0.2:
    data-view-buffer: private
  data-view-byte-length@1.0.2:
    data-view-byte-length: private
  data-view-byte-offset@1.0.1:
    data-view-byte-offset: private
  debug@4.4.0:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  define-data-property@1.1.4:
    define-data-property: private
  define-properties@1.2.1:
    define-properties: private
  delayed-stream@1.0.0:
    delayed-stream: private
  desandro-matches-selector@2.0.2:
    desandro-matches-selector: private
  detect-libc@2.0.3:
    detect-libc: private
  doctrine@2.1.0:
    doctrine: private
  dom-helpers@5.2.1:
    dom-helpers: private
  dunder-proto@1.0.1:
    dunder-proto: private
  es-abstract@1.23.9:
    es-abstract: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-iterator-helpers@1.2.1:
    es-iterator-helpers: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  es-shim-unscopables@1.1.0:
    es-shim-unscopables: private
  es-to-primitive@1.3.0:
    es-to-primitive: private
  esbuild@0.21.5:
    esbuild: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-scope@8.2.0:
    eslint-scope: public
  eslint-visitor-keys@4.2.0:
    eslint-visitor-keys: public
  espree@10.3.0:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  esutils@2.0.3:
    esutils: private
  ev-emitter@2.1.2:
    ev-emitter: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  file-entry-cache@8.0.0:
    file-entry-cache: private
  find-up@5.0.0:
    find-up: private
  fizzy-ui-utils@2.0.7:
    fizzy-ui-utils: private
  flat-cache@4.0.1:
    flat-cache: private
  flatted@3.3.2:
    flatted: private
  follow-redirects@1.15.9:
    follow-redirects: private
  for-each@0.3.5:
    for-each: private
  form-data@4.0.2:
    form-data: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  function.prototype.name@1.1.8:
    function.prototype.name: private
  functions-have-names@1.2.3:
    functions-have-names: private
  get-intrinsic@1.2.7:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  get-size@2.0.3:
    get-size: private
  get-symbol-description@1.1.0:
    get-symbol-description: private
  glob-parent@6.0.2:
    glob-parent: private
  globalthis@1.0.4:
    globalthis: private
  gopd@1.2.0:
    gopd: private
  has-bigints@1.1.0:
    has-bigints: private
  has-flag@4.0.0:
    has-flag: private
  has-property-descriptors@1.0.2:
    has-property-descriptors: private
  has-proto@1.2.0:
    has-proto: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  internal-slot@1.1.0:
    internal-slot: private
  invariant@2.2.4:
    invariant: private
  is-array-buffer@3.0.5:
    is-array-buffer: private
  is-arrayish@0.3.2:
    is-arrayish: private
  is-async-function@2.1.1:
    is-async-function: private
  is-bigint@1.1.0:
    is-bigint: private
  is-boolean-object@1.2.2:
    is-boolean-object: private
  is-callable@1.2.7:
    is-callable: private
  is-core-module@2.16.1:
    is-core-module: private
  is-data-view@1.0.2:
    is-data-view: private
  is-date-object@1.1.0:
    is-date-object: private
  is-extglob@2.1.1:
    is-extglob: private
  is-finalizationregistry@1.1.1:
    is-finalizationregistry: private
  is-generator-function@1.1.0:
    is-generator-function: private
  is-glob@4.0.3:
    is-glob: private
  is-map@2.0.3:
    is-map: private
  is-number-object@1.1.1:
    is-number-object: private
  is-regex@1.2.1:
    is-regex: private
  is-set@2.0.3:
    is-set: private
  is-shared-array-buffer@1.0.4:
    is-shared-array-buffer: private
  is-string@1.1.1:
    is-string: private
  is-symbol@1.1.1:
    is-symbol: private
  is-typed-array@1.1.15:
    is-typed-array: private
  is-weakmap@2.0.2:
    is-weakmap: private
  is-weakref@1.1.1:
    is-weakref: private
  is-weakset@2.0.4:
    is-weakset: private
  isarray@2.0.5:
    isarray: private
  isexe@2.0.0:
    isexe: private
  iterator.prototype@1.1.5:
    iterator.prototype: private
  js-tokens@4.0.0:
    js-tokens: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  jsx-ast-utils@3.3.5:
    jsx-ast-utils: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash.merge@4.6.2:
    lodash.merge: private
  loose-envify@1.4.0:
    loose-envify: private
  masonry-layout@4.2.2:
    masonry-layout: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.8:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  object-assign@4.1.1:
    object-assign: private
  object-inspect@1.13.4:
    object-inspect: private
  object-keys@1.1.1:
    object-keys: private
  object.assign@4.1.7:
    object.assign: private
  object.entries@1.1.8:
    object.entries: private
  object.fromentries@2.0.8:
    object.fromentries: private
  object.values@1.2.1:
    object.values: private
  optionator@0.9.4:
    optionator: private
  outlayer@2.1.1:
    outlayer: private
  own-keys@1.0.1:
    own-keys: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-key@3.1.1:
    path-key: private
  path-parse@1.0.7:
    path-parse: private
  performance-now@2.1.0:
    performance-now: private
  picocolors@1.1.1:
    picocolors: private
  possible-typed-array-names@1.1.0:
    possible-typed-array-names: private
  postcss@8.5.2:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prop-types@15.8.1:
    prop-types: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  raf@3.4.1:
    raf: private
  react-fast-compare@3.2.2:
    react-fast-compare: private
  react-is@16.13.1:
    react-is: private
  react-router@6.29.0(react@19.0.0):
    react-router: private
  react-transition-group@4.4.5(react-dom@19.0.0(react@19.0.0))(react@19.0.0):
    react-transition-group: private
  reflect.getprototypeof@1.0.10:
    reflect.getprototypeof: private
  regenerator-runtime@0.14.1:
    regenerator-runtime: private
  regexp.prototype.flags@1.5.4:
    regexp.prototype.flags: private
  resolve-from@4.0.0:
    resolve-from: private
  resolve@2.0.0-next.5:
    resolve: private
  rollup@4.34.7:
    rollup: private
  safe-array-concat@1.1.3:
    safe-array-concat: private
  safe-push-apply@1.0.0:
    safe-push-apply: private
  safe-regex-test@1.1.0:
    safe-regex-test: private
  scheduler@0.25.0:
    scheduler: private
  semver@6.3.1:
    semver: private
  set-function-length@1.2.2:
    set-function-length: private
  set-function-name@2.0.2:
    set-function-name: private
  set-proto@1.0.0:
    set-proto: private
  shallowequal@1.1.0:
    shallowequal: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  side-channel-list@1.0.0:
    side-channel-list: private
  side-channel-map@1.0.1:
    side-channel-map: private
  side-channel-weakmap@1.0.2:
    side-channel-weakmap: private
  side-channel@1.1.0:
    side-channel: private
  simple-swizzle@0.2.2:
    simple-swizzle: private
  source-map-js@1.2.1:
    source-map-js: private
  source-map-support@0.5.21:
    source-map-support: private
  source-map@0.6.1:
    source-map: private
  string.prototype.matchall@4.0.12:
    string.prototype.matchall: private
  string.prototype.repeat@1.0.0:
    string.prototype.repeat: private
  string.prototype.trim@1.2.10:
    string.prototype.trim: private
  string.prototype.trimend@1.0.9:
    string.prototype.trimend: private
  string.prototype.trimstart@1.0.8:
    string.prototype.trimstart: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  supports-preserve-symlinks-flag@1.0.0:
    supports-preserve-symlinks-flag: private
  type-check@0.4.0:
    type-check: private
  typed-array-buffer@1.0.3:
    typed-array-buffer: private
  typed-array-byte-length@1.0.3:
    typed-array-byte-length: private
  typed-array-byte-offset@1.0.4:
    typed-array-byte-offset: private
  typed-array-length@1.0.7:
    typed-array-length: private
  unbox-primitive@1.1.0:
    unbox-primitive: private
  undici-types@6.21.0:
    undici-types: private
  uri-js@4.4.1:
    uri-js: private
  video-worker@2.2.0:
    video-worker: private
  which-boxed-primitive@1.1.1:
    which-boxed-primitive: private
  which-builtin-type@1.2.1:
    which-builtin-type: private
  which-collection@1.0.2:
    which-collection: private
  which-typed-array@1.1.18:
    which-typed-array: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  yocto-queue@0.1.0:
    yocto-queue: private
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@9.15.4
pendingBuilds: []
prunedAt: Tue, 17 Jun 2025 10:40:20 GMT
publicHoistPattern:
  - '*eslint*'
  - '*prettier*'
registries:
  default: https://registry.npmjs.org/
skipped:
  - '@emnapi/runtime@1.4.0'
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-arm64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@img/sharp-darwin-arm64@0.34.0'
  - '@img/sharp-darwin-x64@0.34.0'
  - '@img/sharp-libvips-darwin-arm64@1.1.0'
  - '@img/sharp-libvips-darwin-x64@1.1.0'
  - '@img/sharp-libvips-linux-arm64@1.1.0'
  - '@img/sharp-libvips-linux-arm@1.1.0'
  - '@img/sharp-libvips-linux-ppc64@1.1.0'
  - '@img/sharp-libvips-linux-s390x@1.1.0'
  - '@img/sharp-libvips-linux-x64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-arm64@1.1.0'
  - '@img/sharp-libvips-linuxmusl-x64@1.1.0'
  - '@img/sharp-linux-arm64@0.34.0'
  - '@img/sharp-linux-arm@0.34.0'
  - '@img/sharp-linux-s390x@0.34.0'
  - '@img/sharp-linux-x64@0.34.0'
  - '@img/sharp-linuxmusl-arm64@0.34.0'
  - '@img/sharp-linuxmusl-x64@0.34.0'
  - '@img/sharp-wasm32@0.34.0'
  - '@img/sharp-win32-ia32@0.34.0'
  - '@rollup/rollup-android-arm-eabi@4.34.7'
  - '@rollup/rollup-android-arm64@4.34.7'
  - '@rollup/rollup-darwin-arm64@4.34.7'
  - '@rollup/rollup-darwin-x64@4.34.7'
  - '@rollup/rollup-freebsd-arm64@4.34.7'
  - '@rollup/rollup-freebsd-x64@4.34.7'
  - '@rollup/rollup-linux-arm-gnueabihf@4.34.7'
  - '@rollup/rollup-linux-arm-musleabihf@4.34.7'
  - '@rollup/rollup-linux-arm64-gnu@4.34.7'
  - '@rollup/rollup-linux-arm64-musl@4.34.7'
  - '@rollup/rollup-linux-loongarch64-gnu@4.34.7'
  - '@rollup/rollup-linux-powerpc64le-gnu@4.34.7'
  - '@rollup/rollup-linux-riscv64-gnu@4.34.7'
  - '@rollup/rollup-linux-s390x-gnu@4.34.7'
  - '@rollup/rollup-linux-x64-gnu@4.34.7'
  - '@rollup/rollup-linux-x64-musl@4.34.7'
  - '@rollup/rollup-win32-arm64-msvc@4.34.7'
  - '@rollup/rollup-win32-ia32-msvc@4.34.7'
  - '@swc/core-darwin-arm64@1.10.16'
  - '@swc/core-darwin-x64@1.10.16'
  - '@swc/core-linux-arm-gnueabihf@1.10.16'
  - '@swc/core-linux-arm64-gnu@1.10.16'
  - '@swc/core-linux-arm64-musl@1.10.16'
  - '@swc/core-linux-x64-gnu@1.10.16'
  - '@swc/core-linux-x64-musl@1.10.16'
  - '@swc/core-win32-arm64-msvc@1.10.16'
  - '@swc/core-win32-ia32-msvc@1.10.16'
  - fsevents@2.3.3
  - tslib@2.8.1
storeDir: C:\Users\<USER>\AppData\Local\pnpm\store\v3
virtualStoreDir: C:\Users\<USER>\dev-skills\client\node_modules\.pnpm
virtualStoreDirMaxLength: 120
