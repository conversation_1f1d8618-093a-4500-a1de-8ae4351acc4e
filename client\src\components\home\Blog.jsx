import React from "react";
import { Link } from "react-router-dom";
import { getBlogPostsByLanguage } from "@/data/blogPosts";
import { useTranslation } from "@/store/languageStore";

export default function Blog() {
  const { currentLanguage } = useTranslation();
  const blogPosts = getBlogPostsByLanguage(currentLanguage).slice(0, 3); // Get first 3 posts

  // Debug logging
  console.log("Blog component - Current language:", currentLanguage);
  console.log("Blog component - Blog posts:", blogPosts.length);
  return (
    <div className="container">
      <div className="row mb-70 mb-sm-50">
        <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
          <h2 className="section-title mb-30 mb-sm-20">
            <span className="text-gray">
              {currentLanguage === "et" ? "Viimased" : "Latest"}
            </span>{" "}
            {currentLanguage === "et" ? "Teadmised" : "Insights"}
            <span className="text-gray">.</span>
          </h2>
          <div className="text-gray">
            {currentLanguage === "et"
              ? "Eksperditeadmised tarkvaraarenduse, mobiilirakenduste, AI ja tehnoloogiasuundade kohta, mis viivad äri edukuseni."
              : "Expert insights on software development, mobile apps, AI, and technology trends that drive business success."}
          </div>
        </div>
        <div className="col-md-2 col-lg-3 text-center text-md-end mt-10 mt-sm-30">
          <Link to={`/blog`} className="section-more">
            {currentLanguage === "et" ? "Kõik Postitused" : "All Posts"}{" "}
            <i className="mi-chevron-right size-14" />
          </Link>
        </div>
      </div>
      <div className="row mt-n30">
        {/* Post Item */}
        {blogPosts.map((post) => (
          <div
            key={post.id}
            className={`post-prev col-md-6 col-lg-4 mt-30 wow fadeInLeft`}
            data-wow-delay={`${post.delay}s`}
          >
            <div className="post-prev-container">
              <div className="post-prev-img">
                <Link to={`/blog-single/${post.slug}`}>
                  <img
                    src={post.imageSrc}
                    width={607}
                    height={358}
                    alt={post.title}
                  />
                </Link>
              </div>
              <h3 className="post-prev-title">
                <Link to={`/blog-single/${post.slug}`}>{post.title}</Link>
              </h3>
              <div className="post-prev-text">{post.excerpt}</div>
              <div className="post-prev-info clearfix">
                <div className="float-start">
                  <a href="#" className="icon-author">
                    <i className="mi-user size-14 align-middle" />
                  </a>
                  <a href="#">{post.author}</a>
                </div>
                <div className="float-end">
                  <i className="mi-calendar size-14 align-middle" />
                  <a href="#">
                    {new Date(post.publishDate).toLocaleDateString()}
                  </a>
                </div>
              </div>
            </div>
          </div>
        ))}
        {/* End Post Item */}
      </div>
    </div>
  );
}
