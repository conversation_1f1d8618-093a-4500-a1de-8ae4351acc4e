#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*) basedir=`cygpath -w "$basedir"`;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/vite@5.4.14_@types+node@22.14.0_terser@5.39.0/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/vite@5.4.14_@types+node@22.14.0_terser@5.39.0/node_modules/vite/node_modules:/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/vite@5.4.14_@types+node@22.14.0_terser@5.39.0/node_modules:/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/vite@5.4.14_@types+node@22.14.0_terser@5.39.0/node_modules/vite/bin/node_modules:/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/vite@5.4.14_@types+node@22.14.0_terser@5.39.0/node_modules/vite/node_modules:/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/vite@5.4.14_@types+node@22.14.0_terser@5.39.0/node_modules:/mnt/c/Users/<USER>/dev-skills/client/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../vite/bin/vite.js" "$@"
fi
