import React from "react";
import PropTypes from "prop-types";
import TypewriterComponent from "typewriter-effect";

export default function TypeWriter({
  strings = [],
  colorClass = "color-primary-1",
}) {
  return (
    <div className={`typewrite ${colorClass}`}>
      <TypewriterComponent
        options={{
          strings: strings,
          autoStart: true,
          loop: true,
        }}
      />
    </div>
  );
}

TypeWriter.propTypes = {
  strings: PropTypes.arrayOf(PropTypes.string),
  colorClass: PropTypes.string,
};
