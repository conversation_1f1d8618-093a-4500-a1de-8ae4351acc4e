import { bmsItems } from "@/data/bms";
import React, { useEffect, useState } from "react";
import { Link } from "react-router-dom";
import { Gallery, Item } from "react-photoswipe-gallery";
import { useTranslation } from "@/store/languageStore";

export default function DevskillsBMS() {
  // Use reactive translation hook
  const { t: translate, currentLanguage } = useTranslation();

  const [currentCategory, setCurrentCategory] = useState("all");
  const [filtered, setFiltered] = useState(bmsItems);
  const [activeModule, setActiveModule] = useState("all");

  // Store the current module translation keys
  const [moduleKeys, setModuleKeys] = useState({
    heading: "module.all.heading",
    subheading: "module.all.subheading",
    text: "module.all.text",
  });

  useEffect(() => {
    if (currentCategory === "all") {
      setFiltered(
        [...bmsItems].filter((item) => !item.categories.includes("all"))
      );
    } else {
      setFiltered(
        [...bmsItems].filter(
          (elm) =>
            elm.categories.includes(currentCategory) &&
            !elm.categories.includes("all")
        )
      );
    }
  }, [currentCategory, currentLanguage]);

  // Function to handle module button click
  const handleModuleClick = (moduleCategory) => {
    setActiveModule(moduleCategory);

    // Update the module keys based on the selected module
    setModuleKeys({
      heading: `module.${moduleCategory}.heading`,
      subheading: `module.${moduleCategory}.subheading`,
      text: `module.${moduleCategory}.text`,
    });
  };

  return (
    <>
      <div className="container">
        {/* Module Description */}
        <div className="row mb-70 mb-sm-50">
          <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
            <h3 className="text-white opacity-05 mb-3 fs-5 text-uppercase">
              {translate("introducing")}
            </h3>
            <h1 className="section-title mb-3">
              <span className="text-white opacity-06 fs-1">DEVSKILLS</span>
              <span className="text-white opacity-09 fs-1">
                {" "}
                {translate(moduleKeys.heading)}
              </span>
            </h1>
            <h3 className="text-white opacity-07 mb-4 fs-5 text-uppercase">
              {translate(moduleKeys.subheading)}
            </h3>
            <div className="text-white opacity-07">
              {translate(moduleKeys.text)}
            </div>
          </div>
        </div>

        {/* Works Filter - Original Rounded Buttons */}
        <div className="works-filter works-filter-elegant text-center mb-50">
          <a
            onClick={() => {
              handleModuleClick("all");
              setCurrentCategory("all");
            }}
            className={`filter ${activeModule === "all" ? "active" : ""}`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.all")}
          </a>

          {/* Core Module */}
          <a
            onClick={() => {
              handleModuleClick("core");
              setCurrentCategory("core");
            }}
            className={`filter ${activeModule === "core" ? "active" : ""}`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.core")}
          </a>

          {/* Accounting Module */}
          <a
            onClick={() => {
              handleModuleClick("accounting");
              setCurrentCategory("accounting");
            }}
            className={`filter ${
              activeModule === "accounting" ? "active" : ""
            }`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.accounting")}
          </a>

          {/* Budget Module */}
          <a
            onClick={() => {
              handleModuleClick("budget");
              setCurrentCategory("budget");
            }}
            className={`filter ${activeModule === "budget" ? "active" : ""}`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.budget")}
          </a>

          {/* HR Module */}
          <a
            onClick={() => {
              handleModuleClick("hr");
              setCurrentCategory("hr");
            }}
            className={`filter ${activeModule === "hr" ? "active" : ""}`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.hr")}
          </a>

          {/* Recruitment Module */}
          <a
            onClick={() => {
              handleModuleClick("recruiting");
              setCurrentCategory("recruiting");
            }}
            className={`filter ${
              activeModule === "recruiting" ? "active" : ""
            }`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.recruiting")}
          </a>

          {/* Production Module */}
          <a
            onClick={() => {
              handleModuleClick("production");
              setCurrentCategory("production");
            }}
            className={`filter ${
              activeModule === "production" ? "active" : ""
            }`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.production")}
          </a>

          {/* Sales Module */}
          <a
            onClick={() => {
              handleModuleClick("sales");
              setCurrentCategory("sales");
            }}
            className={`filter ${activeModule === "sales" ? "active" : ""}`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.sales")}
          </a>

          {/* Quality Control Module */}
          <a
            onClick={() => {
              handleModuleClick("quality");
              setCurrentCategory("quality");
            }}
            className={`filter ${activeModule === "quality" ? "active" : ""}`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.quality")}
          </a>

          {/* Communication Module */}
          <a
            onClick={() => {
              handleModuleClick("communication");
              setCurrentCategory("communication");
            }}
            className={`filter ${
              activeModule === "communication" ? "active" : ""
            }`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.communication")}
          </a>

          {/* Companies Module */}
          <a
            onClick={() => {
              handleModuleClick("companies");
              setCurrentCategory("companies");
            }}
            className={`filter ${activeModule === "companies" ? "active" : ""}`}
            style={{ cursor: "pointer" }}
          >
            {translate("bms.module.companies")}
          </a>
        </div>
        {/* End Works Filter */}
      </div>
      <div
        className="position-relative"
        style={{
          width: "100%",
          maxWidth: "1600px",
          margin: "0 auto",
        }}
      >
        <div
          style={{
            width: "100%",
            position: "relative",
            borderRadius: "20px 20px 0 0",
            overflow: "hidden",
          }}
        >
          <img
            src="/assets/img/devskills-bms.jpg"
            alt="BMS Background"
            style={{
              width: "100%",
              height: "auto",
              display: "block",
              borderRadius: "20px 20px 0 0",
            }}
          />
          <ul
            className="works-grid work-grid-gut-sm hide-titles"
            id="work-grid"
            style={{
              position: "absolute",
              top: 0,
              left: 0,
              width: "100%",
              height: "100%",
              display: "grid",
              gridTemplateColumns: "repeat(4, 1fr)",
              gap: "5px",
              background: "transparent",
              borderRadius: "20px 20px 0 0",
              overflow: "hidden",
              margin: 0,
              padding: 0,
            }}
          >
            <Gallery>
              {filtered.map((item, index) => (
                <li
                  key={index}
                  className={`work-item mix ${item.categories.join(" ")}`}
                  style={{
                    background: "transparent",
                    width: "100%",
                    height: "100%",
                  }}
                >
                  {item.type === "Lightbox" ? (
                    <Item
                      original={item.imgUrl}
                      thumbnail={item.imgUrl}
                      width={400}
                      height={400}
                    >
                      {({ open }) => (
                        <a
                          onClick={open}
                          className="work-lightbox-link mfp-image"
                          style={{
                            display: "flex",
                            flexDirection: "column",
                            width: "100%",
                            height: "100%",
                            borderRadius: "10px",
                            overflow: "hidden",
                          }}
                        >
                          <div
                            className="work-img"
                            style={{
                              flex: 1,
                              background: "transparent",
                            }}
                          />
                          <div
                            className="work-intro"
                            style={{
                              padding: "15px",
                            }}
                          >
                            <h3 className="work-title">
                              {translate(
                                `bms.module.${item.title
                                  .split(" ")[0]
                                  .toLowerCase()}`
                              )}
                            </h3>
                            <div className="work-descr">{item.type}</div>
                          </div>
                        </a>
                      )}
                    </Item>
                  ) : (
                    <Link
                      to={`/portfolio-single/${item.id}`}
                      className="work-ext-link"
                      style={{ background: "transparent" }}
                    >
                      <div
                        className="work-img"
                        style={{
                          height: "400px", // Square dimensions
                          background: "transparent",
                        }}
                      >
                        <div
                          className="work-img-bg"
                          style={{ background: "transparent" }}
                        />
                      </div>
                      <div className="work-intro">
                        <h3 className="work-title">
                          {translate(
                            `bms.module.${item.title
                              .split(" ")[0]
                              .toLowerCase()}`
                          )}
                        </h3>
                        <div className="work-descr">{item.type}</div>
                      </div>
                    </Link>
                  )}
                </li>
              ))}
            </Gallery>
          </ul>
        </div>
      </div>
    </>
  );
}
