import React from "react";
import Header from "@/components/headers/Header";
import Footer from "@/components/footers/Footer";
import { menuItems } from "@/data/menu";
import MetaComponent from "@/components/common/MetaComponent";

const metadata = {
  title: "Ultimation Studio Overview",
  description: "Overview of the Ultimation Studio modules and features",
};

export default function BMSOverviewPage() {
  return (
    <>
      <MetaComponent meta={metadata} />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section className="page-section bg-dark-alpha-50 light-content">
                <div className="container">
                  <h1>BMS Overview</h1>
                  {/* Add your BMS overview content here */}
                </div>
              </section>
            </main>
            <Footer />
          </div>
        </div>
      </div>
    </>
  );
}
