// client\src\components\headers\Header.jsx

import React from "react";
import { Link } from "react-router-dom";
import { toggleMobileMenu } from "@/utils/toggleMobileMenu";
import Nav from "./components/Nav";
import LanguageSelector from "./components/LanguageSelector";
import { useTranslation } from "@/store/languageStore";
import "@/styles/languageSelector.css";

export default function Header({ links }) {
  const { t } = useTranslation();

  // Add error handling for language store
  if (!t) {
    console.warn("Language store not initialized, using fallback");
  }

  return (
    <div className="main-nav-sub full-wrapper">
      {/* Logo  (* Add your text or image to the link tag. Use SVG or PNG image format.
              If you use a PNG logo image, the image resolution must be equal 200% of the visible logo
              image size for support of retina screens. See details in the template documentation. *) */}
      <div className="nav-logo-wrap local-scroll">
        <Link to="/" className="logo font-alt d-flex align-items-center gap-2">
          <img
            src="/assets/img/power-128.png"
            alt="Your Company Logo"
            width={26}
            height={24}
          />
          <span className="mt-1">DEVSKILLS</span>
        </Link>
      </div>

      {/* Mobile Menu Button */}
      <div
        onClick={toggleMobileMenu}
        className="mobile-nav"
        role="button"
        tabIndex={0}
      >
        <i className="mobile-nav-icon" />
        <span className="visually-hidden">Menu</span>
      </div>
      {/* Main Menu */}
      <div className="inner-nav desktop-nav">
        <ul className="clearlist scroll-nav local-scroll justify-content-end scrollspyLinks">
          <Nav links={links} />
          <li className="ms-3 me-2 d-flex align-items-center">
            <LanguageSelector />
          </li>
          <li>
            <a href="https://comanager.ee" className="opacity-1 no-hover">
              <span
                className="btn btn-mod btn-small btn-border-w btn-circle"
                data-btn-animate="y"
              >
                <span className="btn-animate-y">
                  <span className="btn-animate-y-1">Business Comanager</span>
                  <span className="btn-animate-y-2" aria-hidden="true">
                    Business Comanager
                  </span>
                </span>
              </span>
            </a>
          </li>
        </ul>
      </div>
      {/* End Main Menu */}
    </div>
  );
}
