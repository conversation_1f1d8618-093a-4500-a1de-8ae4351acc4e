import { useContactItems } from "@/data/contact";
import React, { useState } from "react";
import { useTranslation } from "@/store/languageStore";
import axios from "axios";
import {
  trackContactFormSubmission,
  trackFormSubmission,
} from "@/utils/analytics";

export default function Contact() {
  const { t } = useTranslation();
  const contactItems = useContactItems();
  const [formData, setFormData] = useState({
    name: "",
    email: "",
    message: "",
  });
  const [formStatus, setFormStatus] = useState({
    submitting: false,
    success: false,
    error: false,
    message: "",
  });
  // Handle form input changes
  const handleInputChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value,
    });
  };

  // Handle form submission
  const handleSubmit = async (e) => {
    e.preventDefault();

    // Set submitting state
    setFormStatus({
      submitting: true,
      success: false,
      error: false,
      message: "",
    });

    try {
      // Use environment-based URL
      const isDevelopment = window.location.hostname === "localhost";
      const endpointUrl = isDevelopment
        ? "http://localhost:4004/api/v1/communication/public/contact-form" // Local dev (keep existing)
        : "https://devskills.ee/api/v1/communication/public/contact-form"; // Production URL
      const apiKey = "9afe34d2134b43e19163c50924df6714";

      // Send the form data to the endpoint
      await axios.post(endpointUrl, formData, {
        headers: {
          "Content-Type": "application/json",
          "X-API-Key": apiKey,
        },
      });

      // Track successful form submission
      trackContactFormSubmission(formData.email, formData.message.length);
      trackFormSubmission("Contact Form", window.location.pathname, true);

      // Handle success
      setFormStatus({
        submitting: false,
        success: true,
        error: false,
        message:
          t("contact.success") ||
          "Thank you! Your message has been sent successfully.",
      });

      // Reset form
      setFormData({
        name: "",
        email: "",
        message: "",
      });
    } catch (error) {
      console.error("Contact form error:", error);

      // Track failed form submission
      trackFormSubmission("Contact Form", window.location.pathname, false);

      // Handle different types of errors
      let errorMessage =
        t("contact.error") ||
        "Sorry, there was an error sending your message. Please try again.";

      if (error.response?.status === 429) {
        errorMessage =
          t("contact.rateLimit") ||
          "Too many requests. Please wait a moment before trying again.";
      } else if (error.response?.status === 400) {
        errorMessage =
          t("contact.validation") || "Please check your input and try again.";
      }

      setFormStatus({
        submitting: false,
        success: false,
        error: true,
        message: errorMessage,
      });
    }
  };

  return (
    <div className="container">
      <div className="row mt-n10 mb-60 mb-xs-40">
        <div className="col-md-10 offset-md-1">
          <div className="row">
            {/* Phone */}
            {contactItems.map((item, index) => (
              <React.Fragment key={index}>
                <div className={`col-md-6 col-lg-4 mb-md-30 `}>
                  <div className="contact-item wow fadeScaleIn">
                    <div className="ci-icon">
                      <i className={item.iconClass} />
                    </div>
                    <h4 className="ci-title">{item.title}</h4>
                    <div className="ci-text large">{item.text}</div>
                    <div className="ci-link">
                      <a
                        href={item.link.url}
                        target={item.link.target}
                        rel={item.link.rel}
                      >
                        {item.link.text}
                      </a>
                    </div>{" "}
                  </div>
                </div>{" "}
              </React.Fragment>
            ))}

            {/* End Email */}
          </div>
        </div>
      </div>
      {/* Contact Form */}
      <div className="row">
        <div className="col-md-10 offset-md-1">
          <form
            onSubmit={handleSubmit}
            className="form contact-form wow fadeInUp wch-unset"
            data-wow-delay=".5s"
            id="contact_form"
          >
            <div className="row">
              <div className="col-md-6">
                {/* Name */}
                <div className="form-group">
                  <label htmlFor="name">{t("contact.name")}</label>
                  <input
                    type="text"
                    name="name"
                    id="name"
                    className="input-lg round form-control"
                    placeholder={t("contact.name.placeholder")}
                    pattern=".{3,100}"
                    required
                    aria-required="true"
                    value={formData.name}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
              <div className="col-md-6">
                {/* Email */}
                <div className="form-group">
                  <label htmlFor="email">{t("contact.email")}</label>
                  <input
                    type="email"
                    name="email"
                    id="email"
                    className="input-lg round form-control"
                    placeholder={t("contact.email.placeholder")}
                    pattern=".{5,100}"
                    required
                    aria-required="true"
                    value={formData.email}
                    onChange={handleInputChange}
                  />
                </div>
              </div>
            </div>
            {/* Message */}
            <div className="form-group">
              <label htmlFor="message">{t("contact.message")}</label>
              <textarea
                name="message"
                id="message"
                className="input-lg round form-control"
                style={{ height: 200 }}
                placeholder={t("contact.message.placeholder")}
                value={formData.message}
                onChange={handleInputChange}
                minLength={5}
                maxLength={5000}
                required
              />
              <div className="form-text text-muted small">
                {formData.message.length}/5000 characters
              </div>
            </div>
            <div className="row">
              <div className="col-sm-6">
                {/* Inform Tip */}
                <div className="form-tip pt-20 pt-sm-0">
                  <i className="icon-info size-16" />
                  {t("contact.terms")}
                </div>
              </div>
              <div className="col-sm-6">
                {/* Send Button */}
                <div className="text-end pt-10">
                  <button
                    type="submit"
                    id="submit_btn"
                    aria-controls="result"
                    className="submit_btn link-hover-anim link-circle-1 align-middle"
                    data-link-animate="y"
                    disabled={formStatus.submitting}
                  >
                    <span className="link-strong link-strong-unhovered">
                      {formStatus.submitting ? "Sending..." : t("contact.send")}
                      <i
                        className="mi-arrow-right size-18 align-middle"
                        aria-hidden="true"
                      ></i>
                    </span>
                    <span
                      className="link-strong link-strong-hovered"
                      aria-hidden="true"
                    >
                      {formStatus.submitting ? "Sending..." : t("contact.send")}
                      <i
                        className="mi-arrow-right size-18 align-middle"
                        aria-hidden="true"
                      ></i>
                    </span>
                  </button>
                </div>
              </div>
            </div>
            <div
              id="result"
              role="region"
              aria-live="polite"
              aria-atomic="true"
              className={`mt-4 p-3 rounded ${
                formStatus.success
                  ? "bg-success-100 text-success-700"
                  : formStatus.error
                  ? "bg-danger-100 text-danger-700"
                  : ""
              }`}
              style={{ display: formStatus.message ? "block" : "none" }}
            >
              {formStatus.message}
            </div>
          </form>
        </div>
      </div>
      {/* End Contact Form */}
    </div>
  );
}
