import React from "react";
import Footer from "@/components/footers/Footer";
import Header5 from "@/components/headers/Header5";
import { menuItems } from "@/data/menu";
import SEO from "@/components/common/SEO";

// JSON-LD structured data for the products page
const productsSchema = {
  "@context": "https://schema.org",
  "@type": "CollectionPage",
  name: "DEVSKILLS Products",
  description:
    "Explore our suite of powerful tools designed to revolutionize how you manage your business.",
  publisher: {
    "@type": "Organization",
    name: "DEVSKILLS",
    logo: {
      "@type": "ImageObject",
      url: "https://devskills.ee/logo.png",
      width: "180",
      height: "60",
    },
  },
  mainEntity: {
    "@type": "ItemList",
    itemListElement: [
      {
        "@type": "ListItem",
        position: 1,
        item: {
          "@type": "SoftwareApplication",
          name: "Ultimation Studio",
          applicationCategory: "BusinessApplication",
          operatingSystem: "Web",
          offers: {
            "@type": "Offer",
            price: "Contact for pricing",
            priceCurrency: "EUR",
          },
          url: "https://devskills.ee/products/bms",
        },
      },
    ],
  },
};

export default function ProductsPage() {
  return (
    <>
      <SEO
        title="Products"
        description="Explore our suite of powerful tools designed to revolutionize how you manage your business."
        canonical="https://devskills.ee/products"
        image="https://devskills.ee/og-images/products.png"
        type="website"
        schema={productsSchema}
        keywords={[
          "business software",
          "management tools",
          "enterprise solutions",
          "Ultimation Studio",
          "BMS",
        ]}
      />
      <div className="appear-animate">
        {/* Navigation panel */}
        <Header5 links={menuItems} />
        {/* End Navigation panel */}

        {/* Head Section */}
        <section className="page-section bg-dark-alfa-70 parallax-3">
          <div className="relative container align-left">
            <div className="row">
              <div className="col-md-8">
                <h1 className="hs-line-11 font-alt mb-20 mb-xs-0">Products</h1>
                <div className="hs-line-4 font-alt">
                  Innovative Business Solutions
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* End Head Section */}

        {/* Products Section */}
        <section className="page-section">
          <div className="container relative">
            <div className="row">
              <div className="col-md-8 offset-md-2 col-lg-8 offset-lg-2">
                <div className="section-text align-center mb-70 mb-xs-40">
                  <h2 className="section-title font-alt mb-30 mb-xxs-10">
                    Our Products
                  </h2>
                  <p>
                    At DEVSKILLS, we develop innovative software solutions that
                    help businesses streamline operations, increase
                    productivity, and achieve their goals. Our flagship product
                    is Ultimation Studio, a comprehensive business management
                    system designed to transform how you run your business.
                  </p>
                </div>
              </div>
            </div>

            {/* Product Card */}
            <div className="row multi-columns-row">
              <div className="col-md-12 col-lg-12">
                <div className="work-item">
                  <div className="work-img">
                    <a href="/products/bms">
                      <img
                        src="/assets/images/bms-preview.jpg"
                        alt="Ultimation Studio"
                      />
                    </a>
                  </div>
                  <div className="work-intro">
                    <h3 className="work-title font-alt">
                      <a href="/products/bms">Ultimation Studio</a>
                    </h3>
                    <div className="work-descr font-inc">
                      The Ultimate Business Management System
                    </div>
                    <div className="mt-4">
                      <a
                        href="/products/bms"
                        className="btn btn-mod btn-border-w btn-round btn-small"
                      >
                        Learn More
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* End Products Section */}

        {/* Call to Action */}
        <section className="page-section bg-dark bg-dark-alfa-90 fullwidth-slider">
          <div className="container relative">
            <div className="align-center">
              <h3 className="banner-heading font-alt">
                Ready to transform your business?
              </h3>
              <div className="local-scroll">
                <a
                  href="/contact"
                  className="btn btn-mod btn-w btn-medium btn-round"
                >
                  Contact Us
                </a>
              </div>
            </div>
          </div>
        </section>
        {/* End Call to Action */}

        {/* Footer */}
        <Footer />
        {/* End Footer */}
      </div>
    </>
  );
}
