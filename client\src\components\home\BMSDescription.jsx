import React from "react";
import { useTranslation } from "@/store/languageStore";
import { bmsItems } from "@/data/bms";

export default function BMSDescription() {
  const { t } = useTranslation();
  const [activeModule, setActiveModule] = React.useState("all");

  // Find the "All Modules" item for default description
  const allModulesItem = bmsItems.find((item) =>
    item.categories.includes("all")
  );
  const [moduleDescription, setModuleDescription] = React.useState(
    allModulesItem?.description || {
      heading: "ULTIMATION STUDIO",
      subheading: "The Ultimate Business Management System",
      text: "Ultimation Studio is our next-generation business management system that combines AI-driven automation with modular flexibility. It features a smart core that manages your business rules, policies, and strategies, while the AI assistant handles tasks on command, monitoring compliance and performance.",
    }
  );

  // Function to handle module button click
  const handleModuleClick = (moduleCategory) => {
    setActiveModule(moduleCategory);

    // Find the module item with the matching category
    const moduleItem = bmsItems.find((item) =>
      item.categories.includes(moduleCategory)
    );

    // Update the description if the module item exists
    if (moduleItem && moduleItem.description) {
      setModuleDescription(moduleItem.description);
    }
  };

  return (
    <section className="page-section bg-dark-alfa-70" id="bms-description">
      <div className="container relative">
        <div className="row">
          <div className="col-md-8 offset-md-2 col-lg-8 offset-lg-2">
            <h1 className="section-title mb-3">
              <span className="text-white opacity-06 fs-1">DEVSKILLS</span>
              <span className="text-white opacity-09 fs-1">
                {" "}
                {moduleDescription.heading}
              </span>
            </h1>
            <h3 className="text-white opacity-08 mb-4">
              {moduleDescription.subheading}
            </h3>
            <div className="section-text text-white mb-5">
              <p className="text-white opacity-08">{moduleDescription.text}</p>
            </div>

            {/* Module buttons */}
            <div className="module-buttons d-flex flex-wrap justify-content-center gap-2 mb-5">
              <button
                className={`btn ${
                  activeModule === "all"
                    ? "btn-mod btn-w"
                    : "btn-mod btn-border-w"
                } btn-round`}
                onClick={() => handleModuleClick("all")}
              >
                {t("bms.module.all")}
              </button>

              {bmsItems
                .filter((item) => !item.categories.includes("all"))
                .map((item, index) => (
                  <button
                    key={index}
                    className={`btn ${
                      activeModule === item.categories[0]
                        ? "btn-mod btn-w"
                        : "btn-mod btn-border-w"
                    } btn-round`}
                    onClick={() => handleModuleClick(item.categories[0])}
                  >
                    {t(`bms.module.${item.categories[0]}`)}
                  </button>
                ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
}
