import Footer from "@/components/footers/Footer";
import Header5 from "@/components/headers/Header5";
import Home5 from "@/components/homes/home-5";
import Hero1 from "@/components/homes/home-5/heros/Hero1";
import { menuItems } from "@/data/menu";
import ParallaxContainer from "@/components/common/ParallaxContainer";
import SEO from "@/components/common/SEO";

// JSON-LD structured data for the homepage
const homeSchema = {
  "@context": "https://schema.org",
  "@type": "Organization",
  name: "Ultimation Studio",
  url: "https://devskills.ee",
  logo: {
    "@type": "ImageObject",
    url: "https://devskills.ee/logo.png",
    width: "180",
    height: "60",
  },
  description:
    "Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity.",
  contactPoint: {
    "@type": "ContactPoint",
    telephone: "******-567-8901",
    contactType: "customer service",
    availableLanguage: ["English", "Estonian"],
  },
  sameAs: [
    "https://www.facebook.com/devskillsee",
    "https://www.linkedin.com/company/devskills-ee",
    "https://twitter.com/DevSkillsEE",
  ],
};

// Additional WebSite schema
const websiteSchema = {
  "@context": "https://schema.org",
  "@type": "WebSite",
  name: "Ultimation Studio",
  url: "https://devskills.ee",
  potentialAction: {
    "@type": "SearchAction",
    target: "https://devskills.ee/search?q={search_term_string}",
    "query-input": "required name=search_term_string",
  },
};
export default function Home5MainDemoMultiPageDark() {
  return (
    <>
      <SEO
        title="Ultimation Studio - Business Management System"
        description="Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity."
        canonical="https://devskills.ee"
        image="https://devskills.ee/og-images/home.png"
        imageAlt="Ultimation Studio - Business Management System"
        imageWidth="1200"
        imageHeight="630"
        schema={[homeSchema, websiteSchema]}
        keywords={[
          "business management system",
          "BMS",
          "productivity",
          "operations",
          "ultimation",
          "studio",
        ]}
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark dark-mode transparent stick-fixed wow-menubar">
              <Header5 links={menuItems} />
            </nav>
            <main id="main">
              <ParallaxContainer
                className="home-section bg-dark-alpha-30 parallax-5 light-content z-index-1 scrollSpysection"
                style={{
                  backgroundImage: "url(/assets/images/demo-elegant/7.jpg)",
                }}
                id="home"
              >
                <Hero1 />
              </ParallaxContainer>

              <Home5 dark />
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
