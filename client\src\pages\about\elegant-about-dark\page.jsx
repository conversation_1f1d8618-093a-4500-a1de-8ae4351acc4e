// client/src/pages/about/elegant-about-dark/page.jsx

import Footer from "@/components/footers/Footer";
import Header from "@/components/headers/Header";
import { Link } from "react-router-dom";
import { menuItems } from "@/data/menu";
import About from "@/components/homes/home-5/About";
import Team from "@/components/homes/home-5/Team";
import MarqueeDark from "@/components/homes/home-5/MarqueeDark";
import { progressData } from "@/data/skills";
import SEO from "@/components/common/SEO";
import { useTranslation } from "@/store/languageStore";
import React from "react";

const onePage = false;
const dark = true;

// JSON-LD structured data for the about page
const aboutSchema = {
  "@context": "https://schema.org",
  "@type": "AboutPage",
  name: "About DevSkills",
  description:
    "Learn about DevSkills, our mission, values, and the team behind our innovative software development services and Business Comanager product.",
  publisher: {
    "@type": "Organization",
    name: "DevSkill<PERSON>",
    logo: "https://devskills.ee/logo.png",
  },
};
export default function ElegantAboutPageDark() {
  const { t, currentLanguage } = useTranslation();
  return (
    <>
      <SEO
        title="About Us"
        description="Learn about DevSkills, our mission, values, and the team behind our innovative software development services and Business Comanager product."
        canonical="https://devskills.ee/about"
        image="https://devskills.ee/og-images/about.png"
        type="article"
        schema={aboutSchema}
      />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header links={menuItems} />
            </nav>
            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage:
                    "url(/assets/images/demo-elegant/section-bg-1.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <h1
                    className="hs-title-3 mb-10 wow fadeInUpShort"
                    data-wow-duration="0.6s"
                  >
                    {t("about.title")}
                  </h1>
                  <div className="row wow fadeIn" data-wow-delay="0.2s">
                    <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3">
                      <p className="section-title-tiny mb-0 opacity-075">
                        {t("about.subtitle")}
                      </p>
                    </div>
                  </div>
                  <div className="spacer-small"></div>
                </div>
              </section>
              <section
                className={`page-section  scrollSpysection pb-0 ${
                  dark ? "bg-dark-1 light-content" : ""
                } `}
                id="about"
              >
                <div className="container position-relative">
                  <div className="row">
                    <div className="col-lg-5 d-flex align-items-center mb-md-50">
                      <div>
                        <div className="wow linesAnimIn" data-splitting="lines">
                          <h2 className="section-title mb-30 mb-sm-20">
                            <span className="text-gray">
                              {t("about.mission.title").split(" ")[0]}
                            </span>{" "}
                            {t("about.mission.title").split(" ")[1]}
                            <span className="text-gray">.</span>
                          </h2>
                          <div className="text-gray mb-30 mb-sm-20">
                            <p className="mb-0">{t("about.mission.text1")}</p>
                            <p className="mt-4">{t("about.mission.text2")}</p>
                          </div>
                        </div>
                      </div>
                    </div>
                    <About />
                  </div>
                </div>
              </section>
              <div className="page-section overflow-hidden">
                <MarqueeDark />
              </div>
              <section
                className="page-section pt-0 pb-0 bg-dark-1 bg-dark-alpha-80 parallax-6 light-content"
                style={{
                  backgroundImage:
                    "url(/assets/images/demo-elegant/backgrounds/bg-dark-1.jpg)",
                }}
              >
                <div className="container position-relative">
                  <div className="row">
                    <div className="col-md-6 col-xl-5">
                      <div className="call-action-1-images pb-60 pb-md-0 mt-n30 mt-md-70 mb-n30 mb-md-70 mb-sm-0">
                        <div className="call-action-1-image-1 round">
                          <img
                            src="/assets/images/demo-elegant/about/business-dashboard.jpeg"
                            width={678}
                            height={840}
                            alt="Business intelligence dashboard showing performance metrics"
                            className="grayscale-effect"
                          />
                        </div>
                        <div className="call-action-1-image-2">
                          <div
                            className="call-action-1-image-2-inner"
                            data-rellax-y=""
                            data-rellax-speed="0.7"
                            data-rellax-percentage="0.427"
                          >
                            <img
                              src="/assets/images/demo-elegant/about/cybernetic-eye.jpg"
                              alt="AI-powered business automation interface"
                              width={300}
                              height={409}
                              className="grayscale-effect"
                            />
                          </div>
                        </div>
                      </div>
                    </div>
                    <div className="col-md-6 offset-xl-1 d-flex align-items-center">
                      <div className="row small-section">
                        <div className="col-xl-11">
                          <h2 className="section-title mb-30 mb-sm-20">
                            {t("about.advantage.title")}
                          </h2>
                          <div className="text-gray mb-30 mb-sm-20">
                            <p className="mb-0">{t("about.advantage.text1")}</p>
                            <p className="mt-4">{t("about.advantage.text2")}</p>
                          </div>
                          <div className="local-scroll">
                            <Link
                              to="/products/bms"
                              className="link-hover-anim link-circle-1 align-middle"
                              data-link-animate="y"
                            >
                              <span className="link-strong link-strong-unhovered">
                                {t("about.advantage.button")}{" "}
                                <i
                                  className="mi-arrow-right size-18 align-middle"
                                  aria-hidden="true"
                                ></i>
                              </span>
                              <span
                                className="link-strong link-strong-hovered"
                                aria-hidden="true"
                              >
                                {t("about.advantage.button")}{" "}
                                <i
                                  className="mi-arrow-right size-18 align-middle"
                                  aria-hidden="true"
                                ></i>
                              </span>
                            </Link>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </section>
              <section
                className={`page-section pb-0  scrollSpysection  ${
                  dark ? "bg-dark-1 light-content" : ""
                } `}
                id="team"
              >
                <Team />
              </section>
              <div className="page-section overflow-hidden">
                <MarqueeDark />
              </div>
              <>
                <section className="page-section bg-dark-1 light-content z-index-1">
                  <div className="container position-relative">
                    <div className="row position-relative">
                      <div className="col-md-6 col-lg-5 mb-md-50 mb-sm-30">
                        <h3 className="section-title mb-30">
                          {t("about.values.title")}
                        </h3>
                        <p className="text-gray mb-0">
                          {t("about.values.custom_text1")}
                        </p>
                        <p className="text-gray mt-4">
                          {t("about.values.custom_text2")}
                        </p>
                      </div>
                      <div className="col-md-6 offset-lg-1 pt-10 pt-sm-0">
                        {/* Bar Item */}
                        {progressData.map((elm, i) => (
                          <div key={i} className="progress tpl-progress">
                            <div
                              className="progress-bar"
                              role="progressbar"
                              style={{ width: `${elm.value}%` }}
                            >
                              <div>{elm.label}, %</div>
                              <span>{elm.value}</span>
                            </div>
                          </div>
                        ))}
                        {/* End Bar Item */}

                        {/* End Bar Item */}
                      </div>
                    </div>
                  </div>
                </section>
                {/* End Skill Section */}
                {/* Divider */}
                <hr className="mt-0 mb-0 white" />
                {/* End Divider */}
                {/* Call Action Section */}
                <section className="page-section bg-dark-1 light-content">
                  <div className="container position-relative">
                    {/* Decorative Waves */}
                    <div className="position-relative">
                      <div
                        className="decoration-21 d-none d-lg-block"
                        data-rellax-y=""
                        data-rellax-speed="0.7"
                        data-rellax-percentage="0.35"
                      >
                        <img
                          src="/assets/images/decoration-3.svg"
                          className="svg-shape"
                          width={148}
                          height={148}
                          alt=""
                        />
                      </div>
                    </div>
                    {/* End Decorative Waves */}
                    <div className="row text-center wow fadeInUp">
                      <div className="col-md-10 offset-md-1 col-lg-6 offset-lg-3">
                        <p className="section-descr mb-50 mb-sm-30">
                          {t("about.cta.text")}
                        </p>
                        <div className="local-scroll">
                          <Link
                            to={`/elegant-contact`}
                            className="btn btn-mod btn-large btn-circle btn-hover-anim"
                          >
                            <span>{t("about.cta.button")}</span>
                          </Link>
                        </div>
                      </div>
                    </div>
                  </div>
                </section>
              </>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
