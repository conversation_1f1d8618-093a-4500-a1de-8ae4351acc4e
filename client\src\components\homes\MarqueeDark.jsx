import React from "react";
import { useTranslation } from "@/store/languageStore";

export default function MarqueeDark() {
  const { t } = useTranslation();
  return (
    <>
      <div className="marquee marquee-style-1 bg-dark-2 mb-30">
        <div className="marquee-track marquee-animation">
          <div>{t("marquee.development1")}</div>
          <div aria-hidden="true">{t("marquee.development2")}</div>
          <div aria-hidden="true">{t("marquee.development3")}</div>
          <div aria-hidden="true">{t("marquee.development4")}</div>
          <div aria-hidden="true">{t("marquee.development5")}</div>
          <div aria-hidden="true">{t("marquee.development6")}</div>
          <div aria-hidden="true">{t("marquee.development1")}</div>
          <div aria-hidden="true">{t("marquee.development2")}</div>
          <div aria-hidden="true">{t("marquee.development3")}</div>
          <div aria-hidden="true">{t("marquee.development4")}</div>
          <div aria-hidden="true">{t("marquee.development5")}</div>
          <div aria-hidden="true">{t("marquee.development6")}</div>
          <div aria-hidden="true">{t("marquee.development1")}</div>
          <div aria-hidden="true">{t("marquee.development2")}</div>
          <div aria-hidden="true">{t("marquee.development3")}</div>
          <div aria-hidden="true">{t("marquee.development4")}</div>
          <div aria-hidden="true">{t("marquee.development5")}</div>
          <div aria-hidden="true">{t("marquee.development6")}</div>
          <div aria-hidden="true">{t("marquee.development1")}</div>
          <div aria-hidden="true">{t("marquee.development2")}</div>
          <div aria-hidden="true">{t("marquee.development3")}</div>
          <div aria-hidden="true">{t("marquee.development4")}</div>
          <div aria-hidden="true">{t("marquee.development5")}</div>
          <div aria-hidden="true">{t("marquee.development6")}</div>
        </div>
      </div>
      {/* End Marquee Text Line */}
      {/* Marquee Text Line */}
      <div className="marquee marquee-style-1 bg-dark-2">
        <div className="marquee-track marquee-animation">
          <div>{t("marquee.values1")}</div>
          <div aria-hidden="true">{t("marquee.values2")}</div>
          <div aria-hidden="true">{t("marquee.values3")}</div>
          <div aria-hidden="true">{t("marquee.values4")}</div>
          <div aria-hidden="true">{t("marquee.values5")}</div>
          <div aria-hidden="true">{t("marquee.values6")}</div>
          <div aria-hidden="true">{t("marquee.values1")}</div>
          <div aria-hidden="true">{t("marquee.values2")}</div>
          <div aria-hidden="true">{t("marquee.values3")}</div>
          <div aria-hidden="true">{t("marquee.values4")}</div>
          <div aria-hidden="true">{t("marquee.values5")}</div>
          <div aria-hidden="true">{t("marquee.values6")}</div>
          <div aria-hidden="true">{t("marquee.values1")}</div>
          <div aria-hidden="true">{t("marquee.values2")}</div>
          <div aria-hidden="true">{t("marquee.values3")}</div>
          <div aria-hidden="true">{t("marquee.values4")}</div>
          <div aria-hidden="true">{t("marquee.values5")}</div>
          <div aria-hidden="true">{t("marquee.values6")}</div>
          <div aria-hidden="true">{t("marquee.values1")}</div>
          <div aria-hidden="true">{t("marquee.values2")}</div>
          <div aria-hidden="true">{t("marquee.values3")}</div>
          <div aria-hidden="true">{t("marquee.values4")}</div>
          <div aria-hidden="true">{t("marquee.values5")}</div>
          <div aria-hidden="true">{t("marquee.values6")}</div>
        </div>
      </div>
    </>
  );
}
