import Footer from "@/components/footers/Footer";

import Header5 from "@/components/headers/Header5";

import { menuItems } from "@/data/menu";
import { useParams } from "react-router-dom";
import Comments from "@/components/blog/Comments";
import Form from "@/components/blog/commentForm/Form";
import Widget1 from "@/components/blog/widgets/Widget1";
import { getBlogPostBySlug } from "@/data/blogPosts";
import { useTranslation } from "@/store/languageStore";
import MetaComponent from "@/components/common/MetaComponent";
export default function ElegantBlogSinglePageDark() {
  let params = useParams();
  const { currentLanguage } = useTranslation();

  // Get blog post by slug from URL params
  const blog = getBlogPostBySlug(params.id, currentLanguage);

  // If no blog post found, show 404 or redirect
  if (!blog) {
    return (
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header5 links={menuItems} />
            </nav>
            <main id="main">
              <section className="page-section bg-dark-1 light-content">
                <div className="container">
                  <div className="row">
                    <div className="col-12 text-center">
                      <h1>Blog Post Not Found</h1>
                      <p>The blog post you're looking for doesn't exist.</p>
                      <a
                        href="/blog"
                        className="btn btn-mod btn-border btn-large btn-round"
                      >
                        Back to Blog
                      </a>
                    </div>
                  </div>
                </div>
              </section>
            </main>
          </div>
        </div>
      </div>
    );
  }

  const metadata = {
    title: `${blog.title} || DevSkills`,
    description: blog.excerpt,
  };
  return (
    <>
      <MetaComponent meta={metadata} />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header5 links={menuItems} />
            </nav>
            <main id="main">
              <section
                className="page-section bg-dark-alpha-50 light-content"
                style={{
                  backgroundImage:
                    "url(/assets/images/demo-elegant/section-bg-1.jpg)",
                }}
                id="home"
              >
                <div className="container position-relative pt-20 pt-sm-20 text-center">
                  <div className="row">
                    <div className="col-lg-10 offset-lg-1">
                      <h1
                        className="hs-title-3a mb-0 wow fadeInUpShort"
                        data-wow-duration="0.6s"
                      >
                        {blog.title}
                      </h1>
                    </div>
                  </div>
                  {/* Author, Categories, Comments */}
                  <div
                    className="blog-item-data mt-30 mt-sm-10 mb-0 wow fadeIn"
                    data-wow-delay="0.2s"
                  >
                    <div className="d-inline-block me-3">
                      <a href="#">
                        <i className="mi-clock size-16" />
                        <span className="visually-hidden">Date:</span>{" "}
                        {new Date(blog.publishDate).toLocaleDateString(
                          "en-US",
                          {
                            year: "numeric",
                            month: "long",
                            day: "numeric",
                          }
                        )}
                      </a>
                    </div>
                    <div className="d-inline-block me-3">
                      <a href="#">
                        <i className="mi-user size-16" />
                        <span className="visually-hidden">Author:</span>{" "}
                        {blog.author}
                      </a>
                    </div>
                    <div className="d-inline-block me-3">
                      <i className="mi-folder size-16" />
                      <span className="visually-hidden">Category:</span>
                      <a href="#">{blog.category}</a>
                    </div>
                    <div className="d-inline-block me-3">
                      <i className="mi-time size-16" />
                      <span className="visually-hidden">Read time:</span>{" "}
                      {blog.readTime}
                    </div>
                  </div>
                  {/* End Author, Categories, Comments */}
                </div>
              </section>
              <section className="page-section bg-dark-1 light-content">
                <div className="container relative">
                  <div className="row">
                    {/* Content */}
                    <div className="col-lg-8 offset-xl-1 mb-md-80 order-first order-lg-last">
                      {/* Post */}
                      <div className="blog-item mb-80 mb-xs-40">
                        <div className="blog-item-body">
                          <div className="mb-40 mb-xs-30">
                            <img
                              src={blog.imageSrc}
                              alt={blog.title}
                              width={1350}
                              height={796}
                            />
                          </div>

                          {/* Blog excerpt */}
                          <div className="lead mb-40">{blog.excerpt}</div>

                          {/* Blog content */}
                          <div
                            className="blog-content"
                            style={{
                              lineHeight: "1.8",
                              fontSize: "16px",
                            }}
                            dangerouslySetInnerHTML={{ __html: blog.content }}
                          />
                        </div>
                      </div>
                      {/* End Post */}
                      {/* Comments */}
                      <div className="mb-80 mb-xs-40">
                        <h4 className="blog-page-title">
                          Comments <small className="number">(3)</small>
                        </h4>
                        <ul className="media-list comment-list clearlist">
                          <Comments />
                        </ul>
                      </div>
                      {/* End Comments */}
                      {/* Add Comment */}
                      <div className="mb-80 mb-xs-40">
                        <h4 className="blog-page-title">Leave a comment</h4>
                        {/* Form */}
                        <Form />
                        {/* End Form */}
                      </div>
                      {/* End Add Comment */}
                      {/* Prev/Next Post */}
                      <div className="clearfix mt-40">
                        <a href="#" className="blog-item-more left">
                          <i className="mi-chevron-left" />
                          &nbsp;Prev post
                        </a>
                        <a href="#" className="blog-item-more right">
                          Next post&nbsp;
                          <i className="mi-chevron-right" />
                        </a>
                      </div>
                      {/* End Prev/Next Post */}
                    </div>
                    {/* End Content */}
                    {/* Sidebar */}
                    <div className="col-lg-4 col-xl-3">
                      <Widget1 searchInputClass="form-control input-lg search-field round" />
                      {/* End Widget */}
                    </div>
                    {/* End Sidebar */}
                  </div>
                </div>
              </section>
            </main>
            <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
              <Footer />
            </footer>
          </div>{" "}
        </div>
      </div>
    </>
  );
}
