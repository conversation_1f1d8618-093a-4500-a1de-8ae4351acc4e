import React, { useEffect } from "react";
import Header from "@/components/headers/Header";
import Footer from "@/components/footers/Footer";
import { menuItems } from "@/data/menu";
import { Link } from "react-router-dom";
import MarqueeDark from "@/components/homes/home-5/MarqueeDark";
import SEO from "@/components/common/SEO";
import "@/styles/ultimation-styles.css";
import { useTranslation } from "@/store/languageStore";

// JSON-LD structured data for the Ultimation Studio Overview page
const ultimationSchema = {
  "@context": "https://schema.org",
  "@type": "Product",
  name: "Ultimation Studio",
  description:
    "The Ultimate Business Management System that combines AI-driven automation with modular flexibility.",
  brand: {
    "@type": "Brand",
    name: "DevSkills",
  },
  offers: {
    "@type": "Offer",
    price: "Contact for pricing",
    priceCurrency: "EUR",
    availability: "https://schema.org/InStock",
  },
  category: "Business Management Software",
};

export default function UltimationStudioOverviewPage() {
  const { t } = useTranslation();

  // Load testimonial slider script
  useEffect(() => {
    // Dynamically import the testimonial slider script
    const script = document.createElement("script");
    script.src = "/scripts/simple-slider.js";
    script.async = true;
    document.body.appendChild(script);

    // Log to confirm script loading
    console.log("Loading slider script");

    // Cleanup function to remove the script when component unmounts
    return () => {
      document.body.removeChild(script);
    };
  }, []);

  return (
    <>
      <SEO
        title="Ultimation Studio - Overview | The Ultimate Business Management System"
        description="Discover Ultimation Studio - the next-generation business management system that combines AI-driven automation with modular flexibility to transform your business operations."
        canonical="https://devskills.ee/products/ultimation/overview"
        image="https://devskills.ee/og-images/ultimation-overview.png"
        type="product"
        schema={ultimationSchema}
        keywords={[
          "Ultimation Studio",
          "business management system",
          "AI automation",
          "modular BMS",
          "enterprise software",
          "DEVSKILLS",
        ]}
      />
      <div className="appear-animate">
        {/* Navigation panel */}
        <nav className="main-nav dark transparent stick-fixed wow-menubar">
          <Header links={menuItems} />
        </nav>
        {/* End Navigation panel */}

        {/* Hero Section */}
        <section className="page-section bg-dark-1 light-content" id="home">
          <div className="container relative">
            <div className="row">
              <div className="col-lg-8 offset-lg-2 text-center">
                <h2
                  className="hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort"
                  data-wow-delay="0.1s"
                >
                  {t("ultimation.overview.title")}
                </h2>
                <h3
                  className="hs-title-1 mb-40 mb-xs-20 wow fadeInUpShort"
                  data-wow-delay="0.2s"
                >
                  {t("ultimation.overview.subtitle")}
                </h3>
                <p
                  className="section-descr mb-50 mb-sm-30 wow fadeInUpShort"
                  data-wow-delay="0.3s"
                >
                  {t("ultimation.overview.intro")}
                </p>
                <div
                  className="local-scroll mb-40 mb-xs-20 wow fadeInUpShort"
                  data-wow-delay="0.4s"
                >
                  <a
                    href="https://ultimation.devskills.ee/auth"
                    className="ultimation-cta-btn btn-large"
                  >
                    {t("ultimation.overview.cta")}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* End Hero Section */}

        {/* Marquee Section */}
        <div className="page-section overflow-hidden bg-dark-alpha-90">
          <MarqueeDark />
        </div>
        {/* End Marquee Section */}

        {/* Key Benefits Section */}
        <section className="page-section bg-dark-1 light-content">
          <div className="container relative">
            <div className="row">
              <div className="col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50">
                <h3 className="section-title-small mb-20 wow fadeInUpShort">
                  {t("ultimation.overview.transform")}
                </h3>
                <p className="section-descr wow fadeInUpShort">
                  {t("ultimation.overview.transform.desc")}
                </p>
              </div>
            </div>

            <div className="row multi-columns-row">
              {/* Benefit 1 */}
              <div className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort">
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-layers"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.benefit1.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.benefit1.text")}</p>
                  </div>
                </div>
              </div>
              {/* End Benefit 1 */}

              {/* Benefit 2 */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.1s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-settings"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.benefit2.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.benefit2.text")}</p>
                  </div>
                </div>
              </div>
              {/* End Benefit 2 */}

              {/* Benefit 3 */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.2s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <div className="ultimation-icon mb-20">
                      <i className="mi-bar-chart"></i>
                    </div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.benefit3.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.benefit3.text")}</p>
                  </div>
                </div>
              </div>
              {/* End Benefit 3 */}
            </div>
          </div>
        </section>
        {/* End Key Benefits Section */}

        {/* Modules Overview Section */}
        <section className="page-section bg-dark-2 light-content">
          <div className="container relative">
            <div className="row">
              <div className="col-lg-8 offset-lg-2 text-center mb-80 mb-sm-50">
                <h3 className="section-title-small mb-20 wow fadeInUpShort">
                  {t("ultimation.overview.modules")}
                </h3>
                <p className="section-descr wow fadeInUpShort">
                  {t("ultimation.overview.modules.desc")}
                </p>
              </div>
            </div>

            <div className="row multi-columns-row">
              {/* Module 1 - Core */}
              <div className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort">
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.module1.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.module1.text")}</p>
                  </div>
                  <div className="mt-20">
                    <Link
                      to="/products/ultimation/core"
                      className="ultimation-cta-btn"
                    >
                      {t("ultimation.overview.learnMore")}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Module 2 - Finance */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.1s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.module2.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.module2.text")}</p>
                  </div>
                  <div className="mt-20">
                    <Link
                      to="/products/ultimation/finance"
                      className="ultimation-cta-btn"
                    >
                      {t("ultimation.overview.learnMore")}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Module 3 - CRM */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.2s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.module3.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.module3.text")}</p>
                  </div>
                  <div className="mt-20">
                    <Link
                      to="/products/ultimation/crm"
                      className="ultimation-cta-btn"
                    >
                      {t("ultimation.overview.learnMore")}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Module 4 - HR */}
              <div className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort">
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.module4.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.module4.text")}</p>
                  </div>
                  <div className="mt-20">
                    <Link
                      to="/products/ultimation/hr"
                      className="ultimation-cta-btn"
                    >
                      {t("ultimation.overview.learnMore")}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Module 5 - Project Management */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.1s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.module5.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.module5.text")}</p>
                  </div>
                  <div className="mt-20">
                    <Link
                      to="/products/ultimation/projects"
                      className="ultimation-cta-btn"
                    >
                      {t("ultimation.overview.learnMore")}
                    </Link>
                  </div>
                </div>
              </div>

              {/* Module 6 - Communication */}
              <div
                className="col-md-4 col-sm-6 mb-60 mb-xs-40 wow fadeInUpShort"
                data-wow-delay="0.2s"
              >
                <div className="alt-features-item text-center ultimation-feature-box">
                  <div>
                    <h3 className="alt-features-title">
                      {t("ultimation.overview.module6.title")}
                    </h3>
                  </div>
                  <div className="alt-features-descr">
                    <p>{t("ultimation.overview.module6.text")}</p>
                  </div>
                  <div className="mt-20">
                    <Link
                      to="/products/ultimation/communication"
                      className="ultimation-cta-btn"
                    >
                      {t("ultimation.overview.learnMore")}
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            <div className="row">
              <div className="col-lg-8 offset-lg-2 text-center mt-40">
                <Link
                  to="/products/ultimation/modules"
                  className="ultimation-cta-btn btn-large"
                >
                  {t("ultimation.overview.exploreModules")}
                </Link>
              </div>
            </div>
          </div>
        </section>
        {/* End Modules Overview Section */}

        {/* Testimonial Section */}
        <section className="page-section bg-dark-2 light-content">
          <div className="container relative">
            <div className="row">
              <div className="col-md-12 text-center">
                <h3 className="section-title-small mb-70 mb-sm-40 wow fadeInUpShort">
                  {t("ultimation.overview.testimonials")}
                </h3>
              </div>
            </div>
          </div>

          <div className="testimonial-marquee" id="testimonial-marquee">
            <div className="testimonial-marquee-track" id="testimonial-track">
              {/* Original testimonials */}
              <div className="testimonial-marquee-item">
                <blockquote>
                  <p>{t("ultimation.overview.testimonial1")}</p>
                  <footer>
                    {t("ultimation.overview.testimonial1.author")}
                  </footer>
                </blockquote>
              </div>

              <div className="testimonial-marquee-item">
                <blockquote>
                  <p>{t("ultimation.overview.testimonial2")}</p>
                  <footer>
                    {t("ultimation.overview.testimonial2.author")}
                  </footer>
                </blockquote>
              </div>

              <div className="testimonial-marquee-item">
                <blockquote>
                  <p>{t("ultimation.overview.testimonial3")}</p>
                  <footer>
                    {t("ultimation.overview.testimonial3.author")}
                  </footer>
                </blockquote>
              </div>

              <div className="testimonial-marquee-item">
                <blockquote>
                  <p>{t("ultimation.overview.testimonial4")}</p>
                  <footer>
                    {t("ultimation.overview.testimonial4.author")}
                  </footer>
                </blockquote>
              </div>

              {/* Cloned testimonials for continuous loop */}
              <div className="testimonial-clone">
                <div className="testimonial-marquee-item">
                  <blockquote>
                    <p>{t("ultimation.overview.testimonial1")}</p>
                    <footer>
                      {t("ultimation.overview.testimonial1.author")}
                    </footer>
                  </blockquote>
                </div>

                <div className="testimonial-marquee-item">
                  <blockquote>
                    <p>{t("ultimation.overview.testimonial2")}</p>
                    <footer>
                      {t("ultimation.overview.testimonial2.author")}
                    </footer>
                  </blockquote>
                </div>

                <div className="testimonial-marquee-item">
                  <blockquote>
                    <p>{t("ultimation.overview.testimonial3")}</p>
                    <footer>
                      {t("ultimation.overview.testimonial3.author")}
                    </footer>
                  </blockquote>
                </div>

                <div className="testimonial-marquee-item">
                  <blockquote>
                    <p>{t("ultimation.overview.testimonial4")}</p>
                    <footer>
                      {t("ultimation.overview.testimonial4.author")}
                    </footer>
                  </blockquote>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* End Testimonial Section */}

        {/* CTA Section */}
        <section className="page-section bg-dark-alpha-90 light-content">
          <div className="container relative">
            <div className="row">
              <div className="col-md-8 offset-md-2 text-center">
                <h3 className="section-title-small mb-30 mb-sm-20 wow fadeInUpShort">
                  {t("ultimation.overview.ready")}
                </h3>
                <p className="section-descr mb-50 mb-sm-30 wow fadeInUpShort">
                  {t("ultimation.overview.ready.desc")}
                </p>
                <div className="local-scroll wow fadeInUpShort">
                  <a
                    href="https://ultimation.devskills.ee/auth"
                    className="ultimation-cta-btn btn-large"
                  >
                    {t("ultimation.overview.getStarted")}
                  </a>
                </div>
              </div>
            </div>
          </div>
        </section>
        {/* End CTA Section */}

        {/* Footer */}
        <footer className="bg-dark-2 light-content footer z-index-1 position-relative">
          <Footer />
        </footer>
        {/* End Footer */}
      </div>
    </>
  );
}
