import { teamMembers2 } from "@/data/team";
import React from "react";
import { useTranslation } from "@/store/languageStore";

export default function Team() {
  const { t } = useTranslation();
  return (
    <div className="container">
      <div className="row mb-70 mb-sm-50">
        <div className="col-md-8 offset-md-2 col-lg-6 offset-lg-3 text-center">
          <h2 className="section-title mb-30 mb-sm-20">
            <span className="text-gray">
              {t("about.team.title").split(" ")[0]}
            </span>{" "}
            {t("about.team.title").split(" ")[1]}
            <span className="text-gray">.</span>
          </h2>
          <div className="text-gray">{t("about.team.description")}</div>
        </div>
      </div>
      <div className="row justify-content-center mt-n40">
        {/* Team item */}
        {teamMembers2.map((member, index) => (
          <div key={index} className="col-md-5 col-lg-4 mt-40 mx-md-4">
            <div className="team-item">
              <div
                className={`team-item-image ${
                  member.name === "Timo Lambing" ? "timo-image-container" : ""
                }`}
              >
                <img
                  src={member.image}
                  width={625}
                  height={767}
                  className="wow scaleOutIn grayscale-effect team-image"
                  data-wow-duration="1.2s"
                  alt={`Image of ${member.name}`}
                  style={{ objectFit: "cover", width: "100%", height: "100%" }}
                />
                <div className="team-item-detail">
                  <div className="team-social-links">
                    {member.socials.map((social, idx) => (
                      <a
                        key={idx}
                        href={social.url}
                        target="_blank"
                        rel="noopener nofollow"
                      >
                        <div className="visually-hidden">{social.name}</div>
                        <i className={`fa-${social.name.toLowerCase()}`} />
                      </a>
                    ))}
                  </div>
                </div>
              </div>
              <div className="team-item-descr">
                <div className="team-item-name">{member.name}</div>
                <div className="team-item-role">{member.role}</div>
              </div>
            </div>
          </div>
        ))}
        {/* End Team item */}
      </div>
    </div>
  );
}
