import React, { useEffect } from "react";
import { useParams } from "react-router-dom";
import Header5 from "@/components/headers/Header5";
import Footer from "@/components/footers/Footer";
import { menuItems } from "@/data/menu";
import MetaComponent from "@/components/common/MetaComponent";

const metadata = {
  title: "Ultimation Studio Module",
  description: "Ultimation Studio module details",
};

export default function BMSModulePage() {
  const { moduleId } = useParams();

  // If the module is 'core', redirect to the Core module page
  useEffect(() => {
    if (moduleId === "core") {
      window.location.href = "/products/bms/core";
    }
  }, [moduleId]);

  return (
    <>
      <MetaComponent meta={metadata} />
      <div className="theme-elegant">
        <div className="dark-mode">
          <div className="page bg-dark-1" id="top">
            <nav className="main-nav dark transparent stick-fixed wow-menubar">
              <Header5 links={menuItems} />
            </nav>
            <main id="main">
              <section className="page-section bg-dark-alpha-50 light-content">
                <div className="container">
                  <h1>BMS Module: {moduleId}</h1>
                  {/* Add your module-specific content here */}
                </div>
              </section>
            </main>
            <Footer />
          </div>
        </div>
      </div>
    </>
  );
}
