import React from "react";
import PropTypes from "prop-types";
import { Helmet } from "react-helmet-async";

/**
 * Enhanced SEO component for managing all meta tags with advanced social media support
 *
 * @param {Object} props - Component props
 * @param {string} props.title - Page title
 * @param {string} props.description - Page description
 * @param {string} props.canonical - Canonical URL
 * @param {string} props.image - OG image URL
 * @param {string} props.type - OG type (website, article, etc.)
 * @param {Object} props.schema - JSON-LD structured data
 * @param {string} props.imageAlt - Alt text for the OG image
 * @param {string} props.imageWidth - Width of the OG image
 * @param {string} props.imageHeight - Height of the OG image
 * @param {string} props.twitterHandle - Twitter handle
 * @param {string} props.publishedAt - Article published date (ISO format)
 * @param {string} props.modifiedAt - Article modified date (ISO format)
 * @param {string} props.author - Article author
 * @param {string[]} props.keywords - Keywords for SEO
 * @param {string} props.locale - Content locale
 */
const SEO = ({
  title = "Ultimation Studio - Business Management System",
  description = "Ultimation Studio offers a comprehensive Business Management System (BMS) to streamline your operations and boost productivity.",
  canonical = "https://devskills.ee",
  image = "https://devskills.ee/og-image.jpg",
  imageAlt = "Ultimation Studio",
  imageWidth = "1200",
  imageHeight = "630",
  type = "website",
  schema = null,
  twitterHandle = "@DevSkillsEE",
  publishedAt = "",
  modifiedAt = "",
  author = "Ultimation Studio",
  keywords = [
    "business management system",
    "BMS",
    "productivity",
    "operations",
  ],
  locale = "en_US",
}) => {
  // Format the title
  const formattedTitle = `${title} | Ultimation Studio`;

  return (
    <Helmet>
      {/* Basic meta tags */}
      <title>{formattedTitle}</title>
      <meta name="description" content={description} />
      <link rel="canonical" href={canonical} />
      <meta name="keywords" content={keywords.join(", ")} />

      {/* Open Graph meta tags for social sharing */}
      <meta property="og:title" content={formattedTitle} />
      <meta property="og:description" content={description} />
      <meta property="og:type" content={type} />
      <meta property="og:url" content={canonical} />
      <meta property="og:image" content={image} />
      <meta property="og:image:alt" content={imageAlt} />
      <meta property="og:image:width" content={imageWidth} />
      <meta property="og:image:height" content={imageHeight} />
      <meta property="og:site_name" content="Ultimation Studio" />
      <meta property="og:locale" content={locale} />

      {/* Article specific meta tags */}
      {type === "article" && publishedAt && (
        <meta property="article:published_time" content={publishedAt} />
      )}
      {type === "article" && modifiedAt && (
        <meta property="article:modified_time" content={modifiedAt} />
      )}
      {type === "article" && author && (
        <meta property="article:author" content={author} />
      )}

      {/* Twitter Card meta tags */}
      <meta name="twitter:card" content="summary_large_image" />
      <meta name="twitter:site" content={twitterHandle} />
      <meta name="twitter:creator" content={twitterHandle} />
      <meta name="twitter:title" content={formattedTitle} />
      <meta name="twitter:description" content={description} />
      <meta name="twitter:image" content={image} />
      <meta name="twitter:image:alt" content={imageAlt} />

      {/* Additional meta tags for SEO */}
      <meta name="robots" content="index, follow, max-image-preview:large" />
      <meta name="googlebot" content="index, follow" />
      <meta httpEquiv="Content-Type" content="text/html; charset=utf-8" />
      <meta name="language" content="English" />

      {/* Mobile specific meta tags */}
      <meta
        name="viewport"
        content="width=device-width, initial-scale=1.0, maximum-scale=5.0"
      />
      <meta name="theme-color" content="#06B6D4" />
      <meta name="apple-mobile-web-app-capable" content="yes" />
      <meta
        name="apple-mobile-web-app-status-bar-style"
        content="black-translucent"
      />

      {/* JSON-LD structured data */}
      {schema && Array.isArray(schema) ? (
        // Handle array of schema objects
        schema.map((schemaItem, index) => (
          <script key={index} type="application/ld+json">
            {JSON.stringify(schemaItem)}
          </script>
        ))
      ) : schema ? (
        // Handle single schema object
        <script type="application/ld+json">{JSON.stringify(schema)}</script>
      ) : null}
    </Helmet>
  );
};

SEO.propTypes = {
  title: PropTypes.string,
  description: PropTypes.string,
  canonical: PropTypes.string,
  image: PropTypes.string,
  imageAlt: PropTypes.string,
  imageWidth: PropTypes.string,
  imageHeight: PropTypes.string,
  type: PropTypes.string,
  schema: PropTypes.oneOfType([
    PropTypes.object,
    PropTypes.arrayOf(PropTypes.object),
  ]),
  twitterHandle: PropTypes.string,
  publishedAt: PropTypes.string,
  modifiedAt: PropTypes.string,
  author: PropTypes.string,
  keywords: PropTypes.arrayOf(PropTypes.string),
  locale: PropTypes.string,
};

export default SEO;
